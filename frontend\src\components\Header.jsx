import React from 'react'
import { BarChart3, Database } from 'lucide-react'

const Header = () => {
  return (
    <header className="bg-white border-b border-gray-200">
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-primary-600 rounded-lg">
              <BarChart3 className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                Pandas Profiling SaaS
              </h1>
              <p className="text-sm text-gray-500">
                Automated data profiling and insights
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Database className="w-4 h-4" />
            <span>MVP Version</span>
          </div>
        </div>
      </div>
    </header>
  )
}

export default Header
