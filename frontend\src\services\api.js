import axios from 'axios'

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000',
  timeout: 300000, // 5 minutes for large file processing
})

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`)
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message)
    
    // Handle different error types
    if (error.response?.status === 413) {
      throw new Error('File too large. Please upload a file smaller than 50MB.')
    } else if (error.response?.status === 400) {
      throw new Error(error.response.data?.detail || 'Invalid file format or data.')
    } else if (error.response?.status === 500) {
      throw new Error('Server error. Please try again later.')
    } else if (error.code === 'ECONNABORTED') {
      throw new Error('Request timeout. The file might be too large or complex to process.')
    } else {
      throw new Error(error.response?.data?.detail || error.message || 'An unexpected error occurred.')
    }
  }
)

/**
 * Upload a file to the server
 * @param {File} file - The file to upload
 * @returns {Promise<Object>} Upload response with file_id and metadata
 */
export const uploadFile = async (file) => {
  const formData = new FormData()
  formData.append('file', file)

  const response = await api.post('/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })

  return response.data
}

/**
 * Generate a profiling report for an uploaded file
 * @param {string} fileId - The ID of the uploaded file
 * @returns {Promise<Object>} Profile generation response
 */
export const generateProfile = async (fileId) => {
  const response = await api.post(`/profile/${fileId}`)
  return response.data
}

/**
 * Get the HTML report for a file
 * @param {string} fileId - The ID of the file
 * @returns {Promise<string>} The report URL
 */
export const getReportUrl = (fileId) => {
  return `${api.defaults.baseURL}/report/${fileId}`
}

/**
 * Delete a file and its associated report
 * @param {string} fileId - The ID of the file to delete
 * @returns {Promise<Object>} Deletion response
 */
export const deleteFile = async (fileId) => {
  const response = await api.delete(`/file/${fileId}`)
  return response.data
}

/**
 * Check API health
 * @returns {Promise<Object>} Health status
 */
export const checkHealth = async () => {
  const response = await api.get('/health')
  return response.data
}

export default api
