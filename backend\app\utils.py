import os
import pandas as pd
from pathlib import Path
from typing import Dict, Any, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def validate_file_size(file_path: Path, max_size_mb: int = 50) -> bool:
    """Validate file size is within limits"""
    file_size = file_path.stat().st_size
    max_size_bytes = max_size_mb * 1024 * 1024
    return file_size <= max_size_bytes

def get_file_info(file_path: Path) -> Dict[str, Any]:
    """Get basic file information"""
    stat = file_path.stat()
    return {
        "size": stat.st_size,
        "created": stat.st_ctime,
        "modified": stat.st_mtime,
        "extension": file_path.suffix.lower()
    }

def read_data_file(file_path: Path) -> pd.DataFrame:
    """Read data file based on extension with error handling"""
    extension = file_path.suffix.lower()
    
    try:
        if extension == '.csv':
            # Try different encodings and separators
            for encoding in ['utf-8', 'latin-1', 'cp1252']:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    logger.info(f"Successfully read CSV with {encoding} encoding")
                    return df
                except UnicodeDecodeError:
                    continue
            raise ValueError("Could not read CSV file with any supported encoding")
            
        elif extension in ['.xlsx', '.xls']:
            df = pd.read_excel(file_path)
            logger.info(f"Successfully read Excel file")
            return df
            
        elif extension == '.json':
            # Try different JSON orientations
            for orient in [None, 'records', 'index', 'values']:
                try:
                    if orient:
                        df = pd.read_json(file_path, orient=orient)
                    else:
                        df = pd.read_json(file_path)
                    logger.info(f"Successfully read JSON with orient={orient}")
                    return df
                except ValueError:
                    continue
            raise ValueError("Could not read JSON file with any supported orientation")
            
        else:
            raise ValueError(f"Unsupported file extension: {extension}")
            
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {str(e)}")
        raise

def validate_dataframe(df: pd.DataFrame) -> Dict[str, Any]:
    """Validate dataframe and return basic info"""
    if df.empty:
        raise ValueError("The uploaded file contains no data")
    
    if len(df.columns) == 0:
        raise ValueError("The uploaded file contains no columns")
    
    # Check for reasonable size limits
    if len(df) > 1000000:  # 1M rows
        raise ValueError("Dataset too large. Maximum 1 million rows supported")
    
    if len(df.columns) > 1000:  # 1K columns
        raise ValueError("Dataset too wide. Maximum 1000 columns supported")
    
    return {
        "rows": len(df),
        "columns": len(df.columns),
        "memory_usage": df.memory_usage(deep=True).sum(),
        "column_names": df.columns.tolist(),
        "dtypes": df.dtypes.to_dict(),
        "null_counts": df.isnull().sum().to_dict()
    }

def cleanup_old_files(directory: Path, max_age_hours: int = 24):
    """Clean up old files to prevent disk space issues"""
    import time
    current_time = time.time()
    max_age_seconds = max_age_hours * 3600
    
    for file_path in directory.glob("*"):
        if file_path.is_file():
            file_age = current_time - file_path.stat().st_mtime
            if file_age > max_age_seconds:
                try:
                    file_path.unlink()
                    logger.info(f"Cleaned up old file: {file_path}")
                except Exception as e:
                    logger.error(f"Error cleaning up file {file_path}: {str(e)}")

def get_sample_data(df: pd.DataFrame, n_rows: int = 5) -> Dict[str, Any]:
    """Get sample data for preview"""
    return {
        "head": df.head(n_rows).to_dict('records'),
        "tail": df.tail(n_rows).to_dict('records'),
        "sample": df.sample(min(n_rows, len(df))).to_dict('records') if len(df) > n_rows else []
    }
