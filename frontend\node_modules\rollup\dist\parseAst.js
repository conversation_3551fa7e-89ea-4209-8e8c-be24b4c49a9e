/*
  @license
	Rollup.js v4.50.2
	Mon, 15 Sep 2025 07:13:55 GMT - commit 76a3b8ede4729a71eb522fc29f7d550a4358827b

	https://github.com/rollup/rollup

	Released under the MIT License.
*/
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

require('./native.js');
const parseAst_js = require('./shared/parseAst.js');
require('node:path');



exports.parseAst = parseAst_js.parseAst;
exports.parseAstAsync = parseAst_js.parseAstAsync;
//# sourceMappingURL=parseAst.js.map
