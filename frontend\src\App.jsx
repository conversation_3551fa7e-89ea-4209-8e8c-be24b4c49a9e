import React, { useState } from 'react'
import Header from './components/Header'
import FileUpload from './components/FileUpload'
import ReportViewer from './components/ReportViewer'
import LoadingSpinner from './components/LoadingSpinner'

function App() {
  const [currentStep, setCurrentStep] = useState('upload') // upload, processing, report
  const [fileData, setFileData] = useState(null)
  const [reportData, setReportData] = useState(null)
  const [isProcessing, setIsProcessing] = useState(false)

  const handleFileUploaded = (data) => {
    setFileData(data)
    setReportData(data)
    setCurrentStep('report')
    setIsProcessing(false)
  }

  const handleReportGenerated = (data) => {
    setReportData(data)
    setCurrentStep('report')
    setIsProcessing(false)
  }

  const handleStartOver = () => {
    setCurrentStep('upload')
    setFileData(null)
    setReportData(null)
    setIsProcessing(false)
  }

  const handleError = (error) => {
    console.error('Error:', error)
    setIsProcessing(false)
    // You could add error state management here
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Progress Steps */}
          <div className="mb-8">
            <div className="flex items-center justify-center space-x-8">
              <div className={`flex items-center space-x-2 ${
                currentStep === 'upload' ? 'text-primary-600' : 
                currentStep === 'processing' || currentStep === 'report' ? 'text-green-600' : 'text-gray-400'
              }`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep === 'upload' ? 'bg-primary-600 text-white' :
                  currentStep === 'processing' || currentStep === 'report' ? 'bg-green-600 text-white' : 'bg-gray-200'
                }`}>
                  1
                </div>
                <span className="font-medium">Upload Data</span>
              </div>
              
              <div className={`w-16 h-0.5 ${
                currentStep === 'processing' || currentStep === 'report' ? 'bg-green-600' : 'bg-gray-200'
              }`}></div>
              
              <div className={`flex items-center space-x-2 ${
                currentStep === 'processing' ? 'text-primary-600' : 
                currentStep === 'report' ? 'text-green-600' : 'text-gray-400'
              }`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep === 'processing' ? 'bg-primary-600 text-white' :
                  currentStep === 'report' ? 'bg-green-600 text-white' : 'bg-gray-200'
                }`}>
                  2
                </div>
                <span className="font-medium">Generate Profile</span>
              </div>
              
              <div className={`w-16 h-0.5 ${
                currentStep === 'report' ? 'bg-green-600' : 'bg-gray-200'
              }`}></div>
              
              <div className={`flex items-center space-x-2 ${
                currentStep === 'report' ? 'text-green-600' : 'text-gray-400'
              }`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep === 'report' ? 'bg-green-600 text-white' : 'bg-gray-200'
                }`}>
                  3
                </div>
                <span className="font-medium">View Report</span>
              </div>
            </div>
          </div>

          {/* Main Content */}
          {currentStep === 'upload' && (
            <FileUpload 
              onFileUploaded={handleFileUploaded}
              onError={handleError}
            />
          )}

          {currentStep === 'processing' && (
            <div className="text-center">
              <LoadingSpinner />
              <h2 className="text-2xl font-semibold text-gray-900 mt-4 mb-2">
                Generating Profile Report
              </h2>
              <p className="text-gray-600 mb-8">
                Analyzing your data and creating comprehensive insights...
              </p>
              {fileData && (
                <div className="card max-w-md mx-auto">
                  <h3 className="font-medium text-gray-900 mb-2">Processing File:</h3>
                  <p className="text-sm text-gray-600">{fileData.filename}</p>
                  <p className="text-sm text-gray-500">
                    {(fileData.size / 1024).toFixed(1)} KB • {fileData.type}
                  </p>
                </div>
              )}
            </div>
          )}

          {currentStep === 'report' && reportData && (
            <ReportViewer 
              reportData={reportData}
              fileData={fileData}
              onStartOver={handleStartOver}
            />
          )}
        </div>
      </main>
    </div>
  )
}

export default App
