import React, { useState } from 'react'
import { Download, RefreshCw, ExternalLink, Database, BarChart3, FileText } from 'lucide-react'
import { getReportUrl, deleteFile } from '../services/api'

const ReportViewer = ({ reportData, fileData, onStartOver }) => {
  const [isDeleting, setIsDeleting] = useState(false)
  
  const reportUrl = getReportUrl(reportData.file_id)
  
  const handleDownload = () => {
    window.open(reportUrl, '_blank')
  }
  
  const handleStartOver = async () => {
    setIsDeleting(true)
    try {
      await deleteFile(reportData.file_id)
    } catch (error) {
      console.error('Error deleting file:', error)
    } finally {
      setIsDeleting(false)
      onStartOver()
    }
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">
          Profile Report Generated
        </h2>
        <p className="text-lg text-gray-600">
          Your data analysis is complete. Explore the insights below.
        </p>
      </div>

      {/* File Info Card */}
      <div className="card">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <FileText className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 text-lg">
                {fileData?.filename || 'Dataset'}
              </h3>
              <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                <span>{formatFileSize(fileData?.size || 0)}</span>
                <span>•</span>
                <span>{fileData?.type?.toUpperCase() || 'Unknown'}</span>
              </div>
            </div>
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={handleDownload}
              className="btn-primary flex items-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>Download Report</span>
            </button>
            <button
              onClick={handleStartOver}
              disabled={isDeleting}
              className="btn-secondary flex items-center space-x-2"
            >
              <RefreshCw className={`w-4 h-4 ${isDeleting ? 'animate-spin' : ''}`} />
              <span>New Analysis</span>
            </button>
          </div>
        </div>
      </div>

      {/* Dataset Overview */}
      {reportData?.dataset_info && (
        <div className="grid md:grid-cols-3 gap-6">
          <div className="card text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <Database className="w-6 h-6 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {reportData.dataset_info.rows?.toLocaleString() || 0}
            </div>
            <div className="text-sm text-gray-600">Rows</div>
          </div>
          
          <div className="card text-center">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <BarChart3 className="w-6 h-6 text-purple-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {reportData.dataset_info.columns || 0}
            </div>
            <div className="text-sm text-gray-600">Columns</div>
          </div>
          
          <div className="card text-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <FileText className="w-6 h-6 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {formatFileSize(reportData.dataset_info.memory_usage || 0)}
            </div>
            <div className="text-sm text-gray-600">Memory Usage</div>
          </div>
        </div>
      )}

      {/* Column Names Preview */}
      {reportData?.dataset_info?.column_names && (
        <div className="card">
          <h3 className="font-semibold text-gray-900 mb-3">Dataset Columns</h3>
          <div className="flex flex-wrap gap-2">
            {reportData.dataset_info.column_names.slice(0, 10).map((column, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm font-medium"
              >
                {column}
              </span>
            ))}
            {reportData.dataset_info.column_names.length > 10 && (
              <span className="px-3 py-1 bg-gray-200 text-gray-600 rounded-full text-sm">
                +{reportData.dataset_info.column_names.length - 10} more
              </span>
            )}
          </div>
        </div>
      )}

      {/* Report Iframe */}
      <div className="card p-0 overflow-hidden">
        <div className="p-4 border-b border-gray-200 flex items-center justify-between">
          <h3 className="font-semibold text-gray-900">Interactive Report</h3>
          <a
            href={reportUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center space-x-1 text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            <ExternalLink className="w-4 h-4" />
            <span>Open in New Tab</span>
          </a>
        </div>
        
        <div className="relative" style={{ height: '600px' }}>
          <iframe
            src={reportUrl}
            className="w-full h-full border-0"
            title="Data Profile Report"
            sandbox="allow-scripts allow-same-origin"
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-center space-x-4">
        <button
          onClick={handleDownload}
          className="btn-primary flex items-center space-x-2"
        >
          <Download className="w-4 h-4" />
          <span>Download Full Report</span>
        </button>
        <button
          onClick={handleStartOver}
          disabled={isDeleting}
          className="btn-secondary flex items-center space-x-2"
        >
          <RefreshCw className={`w-4 h-4 ${isDeleting ? 'animate-spin' : ''}`} />
          <span>Analyze Another Dataset</span>
        </button>
      </div>
    </div>
  )
}

export default ReportViewer
