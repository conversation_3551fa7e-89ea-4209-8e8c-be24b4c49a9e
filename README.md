# Pandas Profiling SaaS MVP

A modern, minimal, and sleek web application for automated data profiling using pandas-profiling.

## Features

- 📊 **Automated Data Profiling**: Upload datasets and get comprehensive profiling reports
- 🚀 **Modern UI**: Clean, minimal interface built with React and Tailwind CSS
- ⚡ **Fast Processing**: High-performance backend with FastAPI
- 📁 **Multiple Formats**: Support for CSV, Excel, and JSON files
- 📱 **Responsive Design**: Works seamlessly on desktop and mobile
- 📈 **Interactive Reports**: Rich HTML reports with charts and statistics

## Tech Stack

### Backend
- **FastAPI**: High-performance Python web framework
- **pandas-profiling**: Automated data profiling
- **pandas**: Data manipulation and analysis
- **uvicorn**: ASGI server

### Frontend
- **React**: Modern JavaScript library
- **Vite**: Fast build tool and dev server
- **Tailwind CSS**: Utility-first CSS framework
- **Axios**: HTTP client for API calls

## Project Structure

```
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── main.py         # FastAPI application
│   │   ├── routers/        # API routes
│   │   ├── services/       # Business logic
│   │   └── models/         # Data models
│   ├── uploads/            # Temporary file storage
│   ├── reports/            # Generated reports
│   └── requirements.txt    # Python dependencies
├── frontend/               # React frontend
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   └── styles/         # CSS styles
│   ├── public/             # Static assets
│   └── package.json        # Node dependencies
└── README.md
```

## Getting Started

### Backend Setup
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload
```

### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```

## MVP Features

1. **File Upload Interface**: Drag-and-drop file upload with progress indication
2. **Data Processing**: Automated profiling with pandas-profiling
3. **Report Display**: Interactive HTML reports embedded in the web interface
4. **Download Reports**: Export reports as HTML files
5. **Error Handling**: User-friendly error messages and validation

## Development Status

This is an MVP (Minimum Viable Product) focused on core functionality with a modern, clean interface.
