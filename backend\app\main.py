from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
import pandas as pd
from ydata_profiling import ProfileReport
import os
import uuid
import aiofiles
from pathlib import Path
import json
from typing import Dict, Any
import shutil
import logging
from .utils import (
    validate_file_size,
    get_file_info,
    read_data_file,
    validate_dataframe,
    cleanup_old_files,
    get_sample_data
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Pandas Profiling SaaS",
    description="Automated data profiling service",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],  # React dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create directories
UPLOAD_DIR = Path("uploads")
REPORTS_DIR = Path("reports")
UPLOAD_DIR.mkdir(exist_ok=True)
REPORTS_DIR.mkdir(exist_ok=True)

# Serve static files
app.mount("/reports", StaticFiles(directory="reports"), name="reports")

@app.get("/")
async def root():
    return {"message": "Pandas Profiling SaaS API", "version": "1.0.0"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload a file and return file info"""
    try:
        # Validate file type
        allowed_extensions = {'.csv', '.xlsx', '.xls', '.json'}
        file_extension = Path(file.filename).suffix.lower()
        
        if file_extension not in allowed_extensions:
            raise HTTPException(
                status_code=400, 
                detail=f"File type {file_extension} not supported. Allowed types: {', '.join(allowed_extensions)}"
            )
        
        # Generate unique filename
        file_id = str(uuid.uuid4())
        filename = f"{file_id}{file_extension}"
        file_path = UPLOAD_DIR / filename
        
        # Save file
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        # Get basic file info
        file_size = len(content)
        
        return {
            "file_id": file_id,
            "filename": file.filename,
            "size": file_size,
            "type": file_extension,
            "status": "uploaded"
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@app.post("/profile/{file_id}")
async def generate_profile(file_id: str):
    """Generate profiling report for uploaded file"""
    try:
        # Find the uploaded file
        uploaded_files = list(UPLOAD_DIR.glob(f"{file_id}.*"))
        if not uploaded_files:
            raise HTTPException(status_code=404, detail="File not found")
        
        file_path = uploaded_files[0]
        file_extension = file_path.suffix.lower()
        
        # Read the data based on file type
        if file_extension == '.csv':
            df = pd.read_csv(file_path)
        elif file_extension in ['.xlsx', '.xls']:
            df = pd.read_excel(file_path)
        elif file_extension == '.json':
            df = pd.read_json(file_path)
        else:
            raise HTTPException(status_code=400, detail="Unsupported file type")
        
        # Generate profile report
        profile = ProfileReport(
            df, 
            title=f"Data Profile Report - {file_path.name}",
            explorative=True,
            minimal=False
        )
        
        # Save report
        report_filename = f"{file_id}_report.html"
        report_path = REPORTS_DIR / report_filename
        profile.to_file(report_path)
        
        # Get basic dataset info
        dataset_info = {
            "rows": len(df),
            "columns": len(df.columns),
            "memory_usage": df.memory_usage(deep=True).sum(),
            "column_names": df.columns.tolist()
        }
        
        return {
            "file_id": file_id,
            "report_url": f"/reports/{report_filename}",
            "dataset_info": dataset_info,
            "status": "completed"
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Profiling failed: {str(e)}")

@app.get("/report/{file_id}")
async def get_report(file_id: str):
    """Get the HTML report for a file"""
    report_filename = f"{file_id}_report.html"
    report_path = REPORTS_DIR / report_filename
    
    if not report_path.exists():
        raise HTTPException(status_code=404, detail="Report not found")
    
    return FileResponse(
        path=report_path,
        media_type="text/html",
        filename=report_filename
    )

@app.delete("/file/{file_id}")
async def delete_file(file_id: str):
    """Delete uploaded file and associated report"""
    try:
        # Delete uploaded file
        uploaded_files = list(UPLOAD_DIR.glob(f"{file_id}.*"))
        for file_path in uploaded_files:
            file_path.unlink()
        
        # Delete report
        report_filename = f"{file_id}_report.html"
        report_path = REPORTS_DIR / report_filename
        if report_path.exists():
            report_path.unlink()
        
        return {"message": "File and report deleted successfully"}
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Deletion failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
