import React, { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Upload, File, AlertCircle, CheckCircle, BarChart3 } from 'lucide-react'
import { uploadFile, generateProfile } from '../services/api'
import LoadingSpinner from './LoadingSpinner'

const FileUpload = ({ onFileUploaded, onError }) => {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [error, setError] = useState(null)

  const onDrop = useCallback(async (acceptedFiles) => {
    if (acceptedFiles.length === 0) return

    const file = acceptedFiles[0]
    setIsUploading(true)
    setError(null)
    setUploadProgress(0)

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 10
        })
      }, 200)

      // Upload file
      const uploadResponse = await uploadFile(file)
      setUploadProgress(100)
      
      // Generate profile
      const profileResponse = await generateProfile(uploadResponse.file_id)
      
      clearInterval(progressInterval)
      onFileUploaded({
        ...uploadResponse,
        ...profileResponse
      })
    } catch (err) {
      setError(err.message || 'Upload failed')
      onError(err)
    } finally {
      setIsUploading(false)
      setUploadProgress(0)
    }
  }, [onFileUploaded, onError])

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'application/json': ['.json']
    },
    maxFiles: 1,
    maxSize: 50 * 1024 * 1024, // 50MB
    disabled: isUploading
  })

  return (
    <div className="max-w-2xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Upload Your Dataset
        </h2>
        <p className="text-lg text-gray-600">
          Get comprehensive insights and profiling for your data in seconds
        </p>
      </div>

      {/* Upload Area */}
      <div
        {...getRootProps()}
        className={`
          relative border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-200
          ${isDragActive && !isDragReject ? 'border-primary-400 bg-primary-50' : ''}
          ${isDragReject ? 'border-red-400 bg-red-50' : ''}
          ${!isDragActive && !isDragReject ? 'border-gray-300 hover:border-primary-400 hover:bg-gray-50' : ''}
          ${isUploading ? 'pointer-events-none opacity-75' : ''}
        `}
      >
        <input {...getInputProps()} />
        
        {isUploading ? (
          <div className="space-y-4">
            <LoadingSpinner />
            <div>
              <p className="text-lg font-medium text-gray-900 mb-2">
                Uploading and Processing...
              </p>
              <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div 
                  className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-600">{uploadProgress}% complete</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex justify-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center">
                <Upload className="w-8 h-8 text-primary-600" />
              </div>
            </div>
            
            <div>
              <p className="text-lg font-medium text-gray-900 mb-2">
                {isDragActive ? 'Drop your file here' : 'Drag & drop your file here'}
              </p>
              <p className="text-gray-600 mb-4">
                or <span className="text-primary-600 font-medium">browse files</span>
              </p>
            </div>
            
            <div className="flex items-center justify-center space-x-6 text-sm text-gray-500">
              <div className="flex items-center space-x-1">
                <File className="w-4 h-4" />
                <span>CSV, Excel, JSON</span>
              </div>
              <div>Max 50MB</div>
            </div>
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg flex items-start space-x-3">
          <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-red-900">Upload Error</h4>
            <p className="text-red-700 text-sm mt-1">{error}</p>
          </div>
        </div>
      )}

      {/* Supported Formats */}
      <div className="mt-8 card">
        <h3 className="font-medium text-gray-900 mb-3">Supported File Formats</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { ext: 'CSV', desc: 'Comma-separated values' },
            { ext: 'XLSX', desc: 'Excel spreadsheet' },
            { ext: 'XLS', desc: 'Legacy Excel format' },
            { ext: 'JSON', desc: 'JavaScript Object Notation' }
          ].map((format) => (
            <div key={format.ext} className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="font-medium text-gray-900">{format.ext}</div>
              <div className="text-xs text-gray-600 mt-1">{format.desc}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Features */}
      <div className="mt-8 grid md:grid-cols-3 gap-6">
        {[
          {
            icon: <BarChart3 className="w-6 h-6" />,
            title: 'Comprehensive Analysis',
            desc: 'Get detailed statistics, distributions, and correlations'
          },
          {
            icon: <CheckCircle className="w-6 h-6" />,
            title: 'Data Quality Check',
            desc: 'Identify missing values, duplicates, and data issues'
          },
          {
            icon: <File className="w-6 h-6" />,
            title: 'Interactive Reports',
            desc: 'Explore your data with rich visualizations and insights'
          }
        ].map((feature, index) => (
          <div key={index} className="text-center">
            <div className="flex justify-center mb-3">
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center text-primary-600">
                {feature.icon}
              </div>
            </div>
            <h4 className="font-medium text-gray-900 mb-2">{feature.title}</h4>
            <p className="text-sm text-gray-600">{feature.desc}</p>
          </div>
        ))}
      </div>
    </div>
  )
}

export default FileUpload
